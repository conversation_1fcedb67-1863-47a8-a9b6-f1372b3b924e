import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * 购物车商品接口
 */
export interface CartItem {
  id: number
  name: string
  price: number
  image: string
  quantity: number
  description?: string
}

/**
 * 购物车状态管理
 */
export const useCartStore = defineStore('cart', () => {
  // 状态
  const items = ref<CartItem[]>([])

  // 计算属性
  const totalItems = computed(() => {
    return items.value.reduce((total, item) => total + item.quantity, 0)
  })

  const totalPrice = computed(() => {
    return items.value.reduce((total, item) => total + (item.price * item.quantity), 0)
  })

  const isEmpty = computed(() => {
    return items.value.length === 0
  })

  // 方法
  /**
   * 添加商品到购物车
   * @param product 商品信息
   */
  const addItem = (product: Omit<CartItem, 'quantity'>) => {
    const existingItem = items.value.find(item => item.id === product.id)
    
    if (existingItem) {
      existingItem.quantity += 1
    } else {
      items.value.push({
        ...product,
        quantity: 1
      })
    }
  }

  /**
   * 从购物车移除商品
   * @param productId 商品ID
   */
  const removeItem = (productId: number) => {
    const index = items.value.findIndex(item => item.id === productId)
    if (index > -1) {
      items.value.splice(index, 1)
    }
  }

  /**
   * 增加商品数量
   * @param productId 商品ID
   */
  const increaseQuantity = (productId: number) => {
    const item = items.value.find(item => item.id === productId)
    if (item) {
      item.quantity += 1
    }
  }

  /**
   * 减少商品数量
   * @param productId 商品ID
   */
  const decreaseQuantity = (productId: number) => {
    const item = items.value.find(item => item.id === productId)
    if (item && item.quantity > 1) {
      item.quantity -= 1
    }
  }

  /**
   * 更新商品数量
   * @param productId 商品ID
   * @param quantity 新数量
   */
  const updateQuantity = (productId: number, quantity: number) => {
    const item = items.value.find(item => item.id === productId)
    if (item && quantity > 0) {
      item.quantity = quantity
    }
  }

  /**
   * 清空购物车
   */
  const clearCart = () => {
    items.value = []
  }

  /**
   * 获取指定商品的数量
   * @param productId 商品ID
   * @returns 商品数量
   */
  const getItemQuantity = (productId: number): number => {
    const item = items.value.find(item => item.id === productId)
    return item ? item.quantity : 0
  }

  /**
   * 检查商品是否在购物车中
   * @param productId 商品ID
   * @returns 是否在购物车中
   */
  const isInCart = (productId: number): boolean => {
    return items.value.some(item => item.id === productId)
  }

  return {
    // 状态
    items,
    
    // 计算属性
    totalItems,
    totalPrice,
    isEmpty,
    
    // 方法
    addItem,
    removeItem,
    increaseQuantity,
    decreaseQuantity,
    updateQuantity,
    clearCart,
    getItemQuantity,
    isInCart
  }
}, {
  // 持久化配置
  persist: {
    key: 'vue-mall-cart',
    storage: localStorage
  }
})
