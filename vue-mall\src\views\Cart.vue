<template>
  <div class="cart">
    <div class="container">
      <div class="page-header">
        <h2>购物车</h2>
        <p v-if="cartStore.items.length > 0">
          共 {{ cartStore.totalItems }} 件商品
        </p>
      </div>

      <!-- 购物车内容 -->
      <div v-if="cartStore.items.length > 0" class="cart-content">
        <!-- 购物车商品列表 -->
        <div class="cart-items">
          <div 
            v-for="item in cartStore.items" 
            :key="item.id" 
            class="cart-item"
          >
            <div class="item-image">
              <img :src="item.image" :alt="item.name" />
            </div>
            <div class="item-info">
              <h3>{{ item.name }}</h3>
              <p class="item-description">{{ item.description || '优质商品，值得拥有' }}</p>
              <div class="item-price">¥{{ item.price }}</div>
            </div>
            <div class="item-controls">
              <div class="quantity-controls">
                <button 
                  @click="cartStore.decreaseQuantity(item.id)"
                  class="quantity-btn"
                  :disabled="item.quantity <= 1"
                >
                  -
                </button>
                <span class="quantity">{{ item.quantity }}</span>
                <button 
                  @click="cartStore.increaseQuantity(item.id)"
                  class="quantity-btn"
                >
                  +
                </button>
              </div>
              <div class="item-total">
                ¥{{ (item.price * item.quantity).toFixed(2) }}
              </div>
              <button 
                @click="cartStore.removeItem(item.id)"
                class="remove-btn"
                title="删除商品"
              >
                🗑️
              </button>
            </div>
          </div>
        </div>

        <!-- 购物车总计 -->
        <div class="cart-summary">
          <div class="summary-card">
            <h3>订单摘要</h3>
            <div class="summary-row">
              <span>商品总计：</span>
              <span>¥{{ cartStore.totalPrice.toFixed(2) }}</span>
            </div>
            <div class="summary-row">
              <span>运费：</span>
              <span>{{ cartStore.totalPrice >= 99 ? '免费' : '¥10.00' }}</span>
            </div>
            <div class="summary-divider"></div>
            <div class="summary-row total">
              <span>总计：</span>
              <span>¥{{ finalTotal.toFixed(2) }}</span>
            </div>
            <button @click="checkout" class="checkout-btn">
              立即结算
            </button>
            <button @click="cartStore.clearCart" class="clear-btn">
              清空购物车
            </button>
          </div>
        </div>
      </div>

      <!-- 空购物车状态 -->
      <div v-else class="empty-cart">
        <div class="empty-icon">🛒</div>
        <h3>购物车是空的</h3>
        <p>快去挑选一些心仪的商品吧！</p>
        <button @click="goToProducts" class="shop-now-btn">
          立即购物
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '../stores/cart'

const router = useRouter()
const cartStore = useCartStore()

/**
 * 计算最终总价（包含运费）
 */
const finalTotal = computed(() => {
  const subtotal = cartStore.totalPrice
  const shipping = subtotal >= 99 ? 0 : 10
  return subtotal + shipping
})

/**
 * 跳转到商品页面
 */
const goToProducts = () => {
  router.push('/products')
}

/**
 * 结算功能
 */
const checkout = () => {
  alert(`订单总计：¥${finalTotal.value.toFixed(2)}\n感谢您的购买！`)
  cartStore.clearCart()
}
</script>

<style scoped>
.cart {
  min-height: calc(100vh - 140px);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h2 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 10px;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
}

/* 购物车内容 */
.cart-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  align-items: start;
}

/* 购物车商品列表 */
.cart-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.cart-item {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.cart-item:hover {
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.item-image {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 20px;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-info {
  flex: 1;
  margin-right: 20px;
}

.item-info h3 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 8px;
}

.item-description {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 10px;
  line-height: 1.5;
}

.item-price {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ff6b6b;
}

.item-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 15px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 5px;
}

.quantity-btn {
  width: 30px;
  height: 30px;
  border: none;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s;
}

.quantity-btn:hover:not(:disabled) {
  background: #409eff;
  color: white;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
}

.item-total {
  font-size: 1.2rem;
  font-weight: 700;
  color: #333;
}

.remove-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s;
}

.remove-btn:hover {
  background: #ffebee;
}

/* 购物车总计 */
.summary-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 20px;
}

.summary-card h3 {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 1rem;
}

.summary-row.total {
  font-size: 1.2rem;
  font-weight: 700;
  color: #333;
}

.summary-divider {
  height: 1px;
  background: #e0e0e0;
  margin: 20px 0;
}

.checkout-btn {
  width: 100%;
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 15px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 10px;
}

.checkout-btn:hover {
  background: #ff5252;
  transform: translateY(-2px);
}

.clear-btn {
  width: 100%;
  background: #f5f5f5;
  color: #666;
  border: none;
  padding: 12px;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
}

.clear-btn:hover {
  background: #e0e0e0;
}

/* 空购物车状态 */
.empty-cart {
  text-align: center;
  padding: 80px 20px;
}

.empty-icon {
  font-size: 5rem;
  margin-bottom: 30px;
}

.empty-cart h3 {
  font-size: 2rem;
  color: #333;
  margin-bottom: 15px;
}

.empty-cart p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.shop-now-btn {
  background: #409eff;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.shop-now-btn:hover {
  background: #337ecc;
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cart-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .cart-item {
    flex-direction: column;
    text-align: center;
  }
  
  .item-image {
    width: 100px;
    height: 100px;
    margin: 0 auto 15px;
  }
  
  .item-info {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .item-controls {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .page-header h2 {
    font-size: 2rem;
  }
  
  .empty-cart {
    padding: 60px 20px;
  }
  
  .empty-icon {
    font-size: 4rem;
  }
  
  .empty-cart h3 {
    font-size: 1.5rem;
  }
}
</style>
