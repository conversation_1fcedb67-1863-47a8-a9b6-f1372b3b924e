<template>
  <div class="profile">
    <div class="container">
      <div class="page-header">
        <h2>个人中心</h2>
        <p>管理您的账户信息和订单</p>
      </div>

      <div class="profile-content">
        <!-- 用户信息卡片 -->
        <div class="user-card">
          <div class="user-avatar">
            <img :src="userInfo.avatar" :alt="userInfo.name" />
          </div>
          <div class="user-info">
            <h3>{{ userInfo.name }}</h3>
            <p class="user-email">{{ userInfo.email }}</p>
            <p class="user-level">{{ userInfo.level }}</p>
          </div>
          <button @click="editProfile" class="edit-btn">编辑资料</button>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">📦</div>
            <div class="stat-info">
              <h4>{{ userStats.totalOrders }}</h4>
              <p>总订单数</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">💰</div>
            <div class="stat-info">
              <h4>¥{{ userStats.totalSpent }}</h4>
              <p>总消费金额</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">⭐</div>
            <div class="stat-info">
              <h4>{{ userStats.points }}</h4>
              <p>积分余额</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">🎁</div>
            <div class="stat-info">
              <h4>{{ userStats.coupons }}</h4>
              <p>可用优惠券</p>
            </div>
          </div>
        </div>

        <!-- 功能菜单 -->
        <div class="menu-grid">
          <div class="menu-section">
            <h3>订单管理</h3>
            <div class="menu-items">
              <div class="menu-item" @click="viewOrders('all')">
                <div class="menu-icon">📋</div>
                <span>全部订单</span>
                <div class="menu-arrow">→</div>
              </div>
              <div class="menu-item" @click="viewOrders('pending')">
                <div class="menu-icon">⏳</div>
                <span>待付款</span>
                <div class="menu-arrow">→</div>
              </div>
              <div class="menu-item" @click="viewOrders('shipping')">
                <div class="menu-icon">🚚</div>
                <span>待收货</span>
                <div class="menu-arrow">→</div>
              </div>
              <div class="menu-item" @click="viewOrders('completed')">
                <div class="menu-icon">✅</div>
                <span>已完成</span>
                <div class="menu-arrow">→</div>
              </div>
            </div>
          </div>

          <div class="menu-section">
            <h3>账户设置</h3>
            <div class="menu-items">
              <div class="menu-item" @click="manageAddresses">
                <div class="menu-icon">📍</div>
                <span>收货地址</span>
                <div class="menu-arrow">→</div>
              </div>
              <div class="menu-item" @click="paymentMethods">
                <div class="menu-icon">💳</div>
                <span>支付方式</span>
                <div class="menu-arrow">→</div>
              </div>
              <div class="menu-item" @click="securitySettings">
                <div class="menu-icon">🔒</div>
                <span>安全设置</span>
                <div class="menu-arrow">→</div>
              </div>
              <div class="menu-item" @click="notifications">
                <div class="menu-icon">🔔</div>
                <span>消息通知</span>
                <div class="menu-arrow">→</div>
              </div>
            </div>
          </div>

          <div class="menu-section">
            <h3>客户服务</h3>
            <div class="menu-items">
              <div class="menu-item" @click="customerService">
                <div class="menu-icon">🎧</div>
                <span>在线客服</span>
                <div class="menu-arrow">→</div>
              </div>
              <div class="menu-item" @click="feedback">
                <div class="menu-icon">📝</div>
                <span>意见反馈</span>
                <div class="menu-arrow">→</div>
              </div>
              <div class="menu-item" @click="help">
                <div class="menu-icon">❓</div>
                <span>帮助中心</span>
                <div class="menu-arrow">→</div>
              </div>
              <div class="menu-item" @click="about">
                <div class="menu-icon">ℹ️</div>
                <span>关于我们</span>
                <div class="menu-arrow">→</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 退出登录 -->
        <div class="logout-section">
          <button @click="logout" class="logout-btn">退出登录</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

/**
 * 用户信息接口
 */
interface UserInfo {
  name: string
  email: string
  avatar: string
  level: string
}

/**
 * 用户统计信息接口
 */
interface UserStats {
  totalOrders: number
  totalSpent: number
  points: number
  coupons: number
}

/**
 * 用户信息
 */
const userInfo = ref<UserInfo>({
  name: '张三',
  email: '<EMAIL>',
  avatar: 'https://via.placeholder.com/100x100?text=用户头像',
  level: 'VIP会员'
})

/**
 * 用户统计信息
 */
const userStats = ref<UserStats>({
  totalOrders: 25,
  totalSpent: 12580,
  points: 1250,
  coupons: 3
})

/**
 * 编辑个人资料
 */
const editProfile = () => {
  alert('编辑个人资料功能')
}

/**
 * 查看订单
 */
const viewOrders = (type: string) => {
  alert(`查看${type}订单`)
}

/**
 * 管理收货地址
 */
const manageAddresses = () => {
  alert('管理收货地址')
}

/**
 * 支付方式
 */
const paymentMethods = () => {
  alert('支付方式管理')
}

/**
 * 安全设置
 */
const securitySettings = () => {
  alert('安全设置')
}

/**
 * 消息通知
 */
const notifications = () => {
  alert('消息通知设置')
}

/**
 * 在线客服
 */
const customerService = () => {
  alert('联系在线客服')
}

/**
 * 意见反馈
 */
const feedback = () => {
  alert('意见反馈')
}

/**
 * 帮助中心
 */
const help = () => {
  alert('帮助中心')
}

/**
 * 关于我们
 */
const about = () => {
  alert('关于我们')
}

/**
 * 退出登录
 */
const logout = () => {
  if (confirm('确定要退出登录吗？')) {
    alert('已退出登录')
    router.push('/')
  }
}
</script>

<style scoped>
.profile {
  min-height: calc(100vh - 140px);
  background: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding-top: 20px;
}

.page-header h2 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 10px;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
}

/* 用户信息卡片 */
.user-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  flex: 1;
}

.user-info h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 5px;
}

.user-email {
  color: #666;
  margin-bottom: 5px;
}

.user-level {
  color: #409eff;
  font-weight: 600;
}

.edit-btn {
  background: #409eff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s;
}

.edit-btn:hover {
  background: #337ecc;
}

/* 统计信息网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 2rem;
}

.stat-info h4 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 5px;
}

.stat-info p {
  color: #666;
  font-size: 0.9rem;
}

/* 功能菜单网格 */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 30px;
}

.menu-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.menu-section h3 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
}

.menu-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.menu-item:hover {
  background: #f8f9fa;
}

.menu-icon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
}

.menu-item span {
  flex: 1;
  color: #333;
  font-weight: 500;
}

.menu-arrow {
  color: #ccc;
  font-weight: bold;
}

/* 退出登录 */
.logout-section {
  text-align: center;
}

.logout-btn {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.logout-btn:hover {
  background: #ff5252;
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header h2 {
    font-size: 2rem;
  }
  
  .user-card {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
    padding: 20px;
  }
  
  .menu-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .menu-section {
    padding: 20px;
  }
}
</style>
