import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import App from './App.vue'
import router from './router'

/**
 * 创建Vue应用实例
 */
const app = createApp(App)

/**
 * 创建Pinia实例并配置持久化插件
 */
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

/**
 * 注册插件
 */
app.use(pinia)
app.use(router)

/**
 * 挂载应用
 */
app.mount('#app')
