# Vue商城项目

一个基于Vue 3 + TypeScript + Vite构建的现代化跨平台商城应用。

## 🚀 项目特性

- ⚡️ **Vue 3** - 使用最新的Vue 3 Composition API
- 🔥 **TypeScript** - 完整的TypeScript支持，提供更好的开发体验
- 📦 **Vite** - 极速的开发服务器和构建工具
- 🛒 **购物车功能** - 完整的购物车管理，支持本地存储
- 🎨 **响应式设计** - 适配桌面端和移动端
- 🗂 **路由管理** - Vue Router 4 单页面应用
- 📊 **状态管理** - Pinia状态管理，支持数据持久化
- 🎯 **TypeScript** - 完整的类型定义和JSDoc注释
- 🔧 **代码规范** - ESLint + Oxlint双重代码检查

## 📱 功能模块

### 🏠 首页
- 轮播图展示
- 热门商品推荐
- 商城特色介绍
- 快速购物入口

### 🛍️ 商品页面
- 商品列表展示
- 分类筛选功能
- 搜索功能
- 商品详情展示
- 一键加入购物车

### 🛒 购物车
- 商品数量管理
- 价格计算
- 运费计算
- 清空购物车
- 立即结算

### 👤 个人中心
- 用户信息展示
- 订单管理
- 账户设置
- 客户服务

## 🛠️ 技术栈

- **前端框架**: Vue 3.5.18
- **开发语言**: TypeScript 5.8.0
- **构建工具**: Vite 7.0.6
- **路由管理**: Vue Router 4.5.1
- **状态管理**: Pinia 3.0.3
- **数据持久化**: pinia-plugin-persistedstate
- **代码规范**: ESLint + Oxlint
- **开发工具**: Vue DevTools

## 📦 安装和运行

### 环境要求
- Node.js >= 20.19.0 或 >= 22.12.0
- npm 或 yarn 或 pnpm

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

### 代码检查
```bash
npm run lint
```

### 类型检查
```bash
npm run type-check
```

## 📁 项目结构

```
vue-mall/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 公共组件
│   ├── views/             # 页面组件
│   │   ├── Home.vue       # 首页
│   │   ├── Products.vue   # 商品页面
│   │   ├── Cart.vue       # 购物车
│   │   └── Profile.vue    # 个人中心
│   ├── router/            # 路由配置
│   │   └── index.ts       # 路由定义
│   ├── stores/            # 状态管理
│   │   └── cart.ts        # 购物车状态
│   ├── App.vue            # 根组件
│   └── main.ts            # 应用入口
├── package.json           # 项目配置
├── vite.config.ts         # Vite配置
├── tsconfig.json          # TypeScript配置
└── README.md              # 项目说明
```

## 🎯 核心功能实现

### 购物车管理
- 使用Pinia进行状态管理
- 支持添加、删除、修改商品数量
- 自动计算总价和运费
- 数据持久化到localStorage

### 响应式设计
- 移动端优先的设计理念
- CSS Grid和Flexbox布局
- 断点适配不同屏幕尺寸
- 触摸友好的交互设计

### 路由管理
- 单页面应用(SPA)架构
- 路由懒加载优化性能
- 路由守卫设置页面标题
- 历史模式路由

## 🔧 开发指南

### 代码规范
项目使用ESLint和Oxlint进行代码检查，确保代码质量：

```bash
# 运行代码检查
npm run lint

# 自动修复可修复的问题
npm run lint:eslint
npm run lint:oxlint
```

### TypeScript支持
项目完全使用TypeScript开发，提供：
- 完整的类型定义
- 接口声明
- JSDoc注释
- 编译时类型检查

### 组件开发
- 使用Vue 3 Composition API
- 单文件组件(SFC)
- 响应式数据管理
- 组件间通信

## 🚀 部署

### 构建优化
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 部署选项
- **静态托管**: Vercel, Netlify, GitHub Pages
- **服务器部署**: Nginx, Apache
- **CDN加速**: 支持各种CDN服务

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [Pinia](https://pinia.vuejs.org/) - Vue状态管理库
- [TypeScript](https://www.typescriptlang.org/) - JavaScript的超集

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues: [GitHub Issues](https://github.com/your-username/vue-mall/issues)
- 邮箱: <EMAIL>

---

⭐ 如果这个项目对你有帮助，请给它一个星标！

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```
