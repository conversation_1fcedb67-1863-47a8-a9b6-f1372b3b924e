<template>
  <div class="products">
    <div class="container">
      <div class="page-header">
        <h2>商品列表</h2>
        <p>发现更多优质商品</p>
      </div>

      <!-- 筛选和搜索 -->
      <div class="filters">
        <div class="search-box">
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="搜索商品..."
            class="search-input"
          />
        </div>
        <div class="category-filters">
          <button 
            v-for="category in categories" 
            :key="category"
            :class="['filter-btn', { active: selectedCategory === category }]"
            @click="selectCategory(category)"
          >
            {{ category }}
          </button>
        </div>
      </div>

      <!-- 商品网格 -->
      <div class="products-grid">
        <div 
          v-for="product in filteredProducts" 
          :key="product.id" 
          class="product-card"
        >
          <div class="product-image">
            <img :src="product.image" :alt="product.name" />
            <div class="product-overlay">
              <button @click="addToCart(product)" class="quick-add-btn">
                快速添加
              </button>
            </div>
          </div>
          <div class="product-info">
            <span class="product-category">{{ product.category }}</span>
            <h3>{{ product.name }}</h3>
            <p class="product-description">{{ product.description }}</p>
            <div class="product-footer">
              <span class="product-price">¥{{ product.price }}</span>
              <button @click="addToCart(product)" class="add-btn">
                加入购物车
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredProducts.length === 0" class="empty-state">
        <div class="empty-icon">📦</div>
        <h3>没有找到相关商品</h3>
        <p>尝试调整搜索条件或浏览其他分类</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useCartStore } from '../stores/cart'

/**
 * 商品接口定义
 */
interface Product {
  id: number
  name: string
  price: number
  image: string
  category: string
  description: string
}

const cartStore = useCartStore()

/**
 * 响应式数据
 */
const searchQuery = ref('')
const selectedCategory = ref('全部')

/**
 * 商品分类
 */
const categories = ref(['全部', '手机', '电脑', '耳机', '平板', '配件'])

/**
 * 商品数据
 */
const products = ref<Product[]>([
  {
    id: 1,
    name: 'iPhone 15 Pro',
    price: 8999,
    image: 'https://via.placeholder.com/300x300?text=iPhone+15+Pro',
    category: '手机',
    description: '最新的iPhone，配备A17 Pro芯片，拍照更出色'
  },
  {
    id: 2,
    name: 'MacBook Air M3',
    price: 9999,
    image: 'https://via.placeholder.com/300x300?text=MacBook+Air',
    category: '电脑',
    description: '轻薄便携，性能强劲的笔记本电脑'
  },
  {
    id: 3,
    name: 'AirPods Pro',
    price: 1999,
    image: 'https://via.placeholder.com/300x300?text=AirPods+Pro',
    category: '耳机',
    description: '主动降噪，音质出色的无线耳机'
  },
  {
    id: 4,
    name: 'iPad Pro',
    price: 6999,
    image: 'https://via.placeholder.com/300x300?text=iPad+Pro',
    category: '平板',
    description: '专业级平板，支持Apple Pencil'
  },
  {
    id: 5,
    name: 'Samsung Galaxy S24',
    price: 7999,
    image: 'https://via.placeholder.com/300x300?text=Galaxy+S24',
    category: '手机',
    description: '三星旗舰手机，AI功能强大'
  },
  {
    id: 6,
    name: 'Dell XPS 13',
    price: 8999,
    image: 'https://via.placeholder.com/300x300?text=Dell+XPS+13',
    category: '电脑',
    description: '商务办公首选，轻薄高性能'
  },
  {
    id: 7,
    name: 'Sony WH-1000XM5',
    price: 2299,
    image: 'https://via.placeholder.com/300x300?text=Sony+WH-1000XM5',
    category: '耳机',
    description: '业界领先的降噪头戴式耳机'
  },
  {
    id: 8,
    name: 'Magic Keyboard',
    price: 999,
    image: 'https://via.placeholder.com/300x300?text=Magic+Keyboard',
    category: '配件',
    description: 'iPad专用键盘，提升办公效率'
  }
])

/**
 * 过滤后的商品列表
 */
const filteredProducts = computed(() => {
  let filtered = products.value

  // 按分类筛选
  if (selectedCategory.value !== '全部') {
    filtered = filtered.filter(product => product.category === selectedCategory.value)
  }

  // 按搜索关键词筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(product => 
      product.name.toLowerCase().includes(query) ||
      product.description.toLowerCase().includes(query)
    )
  }

  return filtered
})

/**
 * 选择分类
 */
const selectCategory = (category: string) => {
  selectedCategory.value = category
}

/**
 * 添加商品到购物车
 */
const addToCart = (product: Product) => {
  cartStore.addItem(product)
  alert(`${product.name} 已添加到购物车！`)
}
</script>

<style scoped>
.products {
  min-height: calc(100vh - 140px);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h2 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 10px;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
}

/* 筛选区域 */
.filters {
  margin-bottom: 40px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-box {
  display: flex;
  justify-content: center;
}

.search-input {
  width: 100%;
  max-width: 400px;
  padding: 12px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.3s;
}

.search-input:focus {
  border-color: #409eff;
}

.category-filters {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-btn {
  padding: 8px 20px;
  border: 2px solid #e0e0e0;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

/* 商品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.product-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.quick-add-btn {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s;
}

.quick-add-btn:hover {
  background: #ff5252;
}

.product-info {
  padding: 20px;
}

.product-category {
  display: inline-block;
  background: #f0f0f0;
  color: #666;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  margin-bottom: 10px;
}

.product-info h3 {
  font-size: 1.2rem;
  margin-bottom: 8px;
  color: #333;
}

.product-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 15px;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 1.3rem;
  font-weight: 700;
  color: #ff6b6b;
}

.add-btn {
  background: #409eff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s;
}

.add-btn:hover {
  background: #337ecc;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.empty-state h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 10px;
}

.empty-state p {
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header h2 {
    font-size: 2rem;
  }
  
  .filters {
    align-items: center;
  }
  
  .category-filters {
    justify-content: center;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }
}
</style>
