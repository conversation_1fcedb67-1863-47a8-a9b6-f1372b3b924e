<template>
  <div class="home">
    <div class="container">
      <!-- 轮播图区域 -->
      <section class="hero">
        <div class="hero-content">
          <h2>欢迎来到Vue商城</h2>
          <p>发现优质商品，享受购物乐趣</p>
          <button class="cta-button" @click="goToProducts">立即购物</button>
        </div>
      </section>

      <!-- 特色商品区域 -->
      <section class="featured-products">
        <h3>热门商品</h3>
        <div class="products-grid">
          <div 
            v-for="product in featuredProducts" 
            :key="product.id" 
            class="product-card"
            @click="addToCart(product)"
          >
            <div class="product-image">
              <img :src="product.image" :alt="product.name" />
            </div>
            <div class="product-info">
              <h4>{{ product.name }}</h4>
              <p class="product-price">¥{{ product.price }}</p>
              <button class="add-to-cart-btn">加入购物车</button>
            </div>
          </div>
        </div>
      </section>

      <!-- 商城特色 -->
      <section class="features">
        <h3>为什么选择我们</h3>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">🚚</div>
            <h4>快速配送</h4>
            <p>24小时内发货，3天内送达</p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🛡️</div>
            <h4>品质保证</h4>
            <p>正品保证，7天无理由退换</p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">💰</div>
            <h4>优惠价格</h4>
            <p>天天低价，会员专享折扣</p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🎧</div>
            <h4>贴心服务</h4>
            <p>24小时客服，随时为您服务</p>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '../stores/cart'

/**
 * 商品接口定义
 */
interface Product {
  id: number
  name: string
  price: number
  image: string
}

const router = useRouter()
const cartStore = useCartStore()

/**
 * 特色商品数据
 */
const featuredProducts = ref<Product[]>([
  {
    id: 1,
    name: 'iPhone 15 Pro',
    price: 8999,
    image: 'https://via.placeholder.com/200x200?text=iPhone+15+Pro'
  },
  {
    id: 2,
    name: 'MacBook Air M3',
    price: 9999,
    image: 'https://via.placeholder.com/200x200?text=MacBook+Air'
  },
  {
    id: 3,
    name: 'AirPods Pro',
    price: 1999,
    image: 'https://via.placeholder.com/200x200?text=AirPods+Pro'
  },
  {
    id: 4,
    name: 'iPad Pro',
    price: 6999,
    image: 'https://via.placeholder.com/200x200?text=iPad+Pro'
  }
])

/**
 * 跳转到商品页面
 */
const goToProducts = () => {
  router.push('/products')
}

/**
 * 添加商品到购物车
 */
const addToCart = (product: Product) => {
  cartStore.addItem(product)
  alert(`${product.name} 已添加到购物车！`)
}
</script>

<style scoped>
.home {
  min-height: calc(100vh - 140px);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 英雄区域 */
.hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
  border-radius: 12px;
  margin-bottom: 60px;
}

.hero-content h2 {
  font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 700;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.cta-button {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 15px 30px;
  font-size: 1.1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 600;
}

.cta-button:hover {
  background: #ff5252;
  transform: translateY(-2px);
}

/* 特色商品区域 */
.featured-products {
  margin-bottom: 60px;
}

.featured-products h3 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 40px;
  color: #333;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.product-image {
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 20px;
}

.product-info h4 {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: #333;
}

.product-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ff6b6b;
  margin-bottom: 15px;
}

.add-to-cart-btn {
  width: 100%;
  background: #409eff;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s;
  font-weight: 500;
}

.add-to-cart-btn:hover {
  background: #337ecc;
}

/* 特色功能区域 */
.features {
  margin-bottom: 60px;
}

.features h3 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 40px;
  color: #333;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-item {
  text-align: center;
  padding: 30px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.feature-item h4 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  color: #333;
}

.feature-item p {
  color: #666;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-content h2 {
    font-size: 2rem;
  }
  
  .hero-content p {
    font-size: 1rem;
  }
  
  .products-grid,
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .featured-products h3,
  .features h3 {
    font-size: 2rem;
  }
}
</style>
